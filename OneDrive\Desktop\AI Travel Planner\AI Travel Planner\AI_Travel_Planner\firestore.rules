rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Rules for AiTrips collection
    match /AiTrips/{tripId} {
      // Allow read and write if user is authenticated and the trip belongs to them
      allow read, write: if request.auth != null && 
                         request.auth.token.email == resource.data.userEmail;
      
      // Allow create if user is authenticated and they're setting their own email
      allow create: if request.auth != null && 
                    request.auth.token.email == request.resource.data.userEmail;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
